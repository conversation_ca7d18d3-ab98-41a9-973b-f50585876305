"""
Background task for processing dividends when record dates pass
"""
from datetime import date, datetime
from decimal import Decimal

from app.db.session import async_session
from app.models.dividend_info import DividendInfo
from app.models.portfolio_entry import PortfolioEntry
from app.models.received_dividend import ReceivedDividend
from sqlalchemy import and_
from sqlalchemy.future import select


async def process_expired_dividends():
    """
    Process dividends for trading codes where the next_record_date has passed
    """
    async with async_session() as db:
        try:
            today = date.today()

            # Find all dividend infos where next_record_date is today or in the past
            # and next_record_date is not null
            result = await db.execute(
                select(DividendInfo).where(
                    and_(
                        DividendInfo.next_record_date.isnot(None),
                        DividendInfo.next_record_date <= today
                    )
                )
            )
            expired_dividend_infos = result.scalars().all()

            for dividend_info in expired_dividend_infos:
                await process_single_dividend(db, dividend_info)

            await db.commit()

        except Exception as e:
            await db.rollback()
            print(f"Error processing dividends: {e}")
            raise


async def process_single_dividend(db, dividend_info: DividendInfo):
    """
    Process a single dividend info record
    """
    # Calculate share count on the record date
    # We need to get all portfolio entries for this trading code up to the record date
    entries_result = await db.execute(
        select(PortfolioEntry).where(
            and_(
                PortfolioEntry.portfolio_id == dividend_info.portfolio_id,
                PortfolioEntry.trading_code_id == dividend_info.trading_code_id,
                PortfolioEntry.transaction_date <= dividend_info.next_record_date
            )
        )
    )
    entries = entries_result.scalars().all()

    # Calculate total shares held on record date
    total_shares = 0
    for entry in entries:
        if entry.transaction_type == "buy":
            total_shares += entry.unit_count
        else:  # sell
            total_shares -= entry.unit_count

    # Only process if user has shares
    if total_shares > 0 and dividend_info.dividend_per_share > 0:
        # Calculate dividend amounts
        gross_dividend = Decimal(str(total_shares)) * dividend_info.dividend_per_share
        tax_amount = gross_dividend * Decimal('0.10')  # 10% tax
        net_dividend = gross_dividend - tax_amount

        # Create received dividend record
        received_dividend = ReceivedDividend(
            portfolio_id=dividend_info.portfolio_id,
            trading_code_id=dividend_info.trading_code_id,
            record_date=dividend_info.next_record_date,
            share_count=total_shares,
            dividend_per_share=dividend_info.dividend_per_share,
            gross_dividend=gross_dividend,
            tax_amount=tax_amount,
            net_dividend=net_dividend
        )

        db.add(received_dividend)

    # Update dividend info: move next_record_date to last_record_date and clear next fields
    dividend_info.last_record_date = dividend_info.next_record_date
    dividend_info.next_record_date = None
    dividend_info.dividend_per_share = Decimal('0.00')
    dividend_info.updated_at = datetime.now()


def calculate_receivable_dividend(share_count: int, dividend_per_share: Decimal) -> Decimal:
    """
    Calculate receivable dividend amount with 10% tax deduction
    """
    if share_count <= 0 or dividend_per_share <= 0:
        return Decimal('0.00')

    gross_dividend = Decimal(str(share_count)) * dividend_per_share
    tax_amount = gross_dividend * Decimal('0.10')  # 10% tax
    net_dividend = gross_dividend - tax_amount

    return net_dividend
