import asyncio
import signal
import sys

import uvicorn
# Configure logging
from app.core.logging_config import get_logger, setup_logging
# Initialize database models
from app.db.init_db import init_models
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

logger = setup_logging()

init_models()  # Call this to ensure all models are loaded

from app.api.api import api_router
from app.core.config import settings

# Store background tasks for clean shutdown
background_tasks = set()

app = FastAPI(
    title="DSE Analyzen API",
    description="Backend API for DSE Analyzen",
    version="0.1.0",
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(api_router, prefix=settings.API_V1_STR)

# Start background tasks
@app.on_event("startup")
async def startup_event():
    from app.core.scheduler import (schedule_dividend_processing,
                                    schedule_live_price_updates)

    # Start the live price updates background task
    price_task = asyncio.create_task(schedule_live_price_updates())
    background_tasks.add(price_task)
    price_task.add_done_callback(background_tasks.discard)

    # Start the dividend processing background task
    dividend_task = asyncio.create_task(schedule_dividend_processing())
    background_tasks.add(dividend_task)
    dividend_task.add_done_callback(background_tasks.discard)

    logger.info("Started background task schedulers (live prices and dividend processing)")

# Handle shutdown gracefully
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down server, cancelling background tasks...")
    # Cancel all running tasks
    for task in background_tasks:
        task.cancel()

    # Wait for all tasks to be cancelled
    if background_tasks:
        await asyncio.gather(*background_tasks, return_exceptions=True)

    logger.info("All background tasks have been cancelled")

if __name__ == "__main__":
    # Handle keyboard interrupts gracefully
    def handle_exit(sig, frame):
        logger.info(f"Received exit signal {sig}, shutting down...")
        sys.exit(0)

    signal.signal(signal.SIGINT, handle_exit)
    signal.signal(signal.SIGTERM, handle_exit)

    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
